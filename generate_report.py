#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np

def parse_monitor_data(filename):
    """
    Parse monitoring data CSV file
    """
    data = []
    current_timestamp = None
    
    with open(filename, 'r') as file:
        for line in file:
            line = line.strip()
            if not line:
                continue
                
            # Parse timestamp line
            if line.startswith('timestamp,'):
                current_timestamp = float(line.split(',')[1].strip())
            
            # Parse process data line
            elif line.startswith('name,') and current_timestamp is not None:
                parts = [part.strip() for part in line.split(',')]
                if len(parts) >= 8:
                    process_name = parts[1]
                    pid = int(parts[3])
                    cpu_usage = float(parts[5])
                    mem_usage = float(parts[7])
                    
                    data.append({
                        'timestamp': current_timestamp,
                        'process_name': process_name,
                        'pid': pid,
                        'cpu_usage': cpu_usage,
                        'mem_usage': mem_usage
                    })
    
    return pd.DataFrame(data)

def calculate_statistics(df, metric):
    """
    Calculate statistics: mean, min/max, median, p99, p90
    """
    stats = {}
    for process in df['process_name'].unique():
        process_data = df[df['process_name'] == process][metric]
        stats[process] = {
            'mean': process_data.mean(),
            'min': process_data.min(),
            'max': process_data.max(),
            'median': process_data.median(),
            'p90': process_data.quantile(0.9),
            'p99': process_data.quantile(0.99),
            'std': process_data.std()
        }
    return stats

def write_statistics_to_markdown(cpu_stats, mem_stats, df, filename='analysis_report.md'):
    """
    Write statistics to markdown file
    """
    with open(filename, 'w', encoding='utf-8') as f:
        # Write header
        f.write("# Process Monitoring Analysis Report\n\n")
        
        # Write basic info
        f.write("## Basic Information\n\n")
        f.write(f"- **Total records**: {len(df)}\n")
        f.write(f"- **Number of processes**: {df['process_name'].nunique()}\n")
        f.write(f"- **Time range**: {(df['timestamp'].max() - df['timestamp'].min()):.1f} seconds\n")
        f.write(f"- **Process list**: {', '.join(df['process_name'].unique())}\n\n")
        
        # Write CPU statistics
        f.write("## CPU Usage Statistics (%)\n\n")
        f.write("| Process Name | Mean | Min | Max | Median | P90 | P99 | Std |\n")
        f.write("|--------------|------|-----|-----|--------|-----|-----|-----|\n")
        
        for process, stat in cpu_stats.items():
            f.write(f"| {process} | {stat['mean']:.2f} | {stat['min']:.2f} | {stat['max']:.2f} | "
                   f"{stat['median']:.2f} | {stat['p90']:.2f} | {stat['p99']:.2f} | {stat['std']:.2f} |\n")
        
        # Write Memory statistics
        f.write("\n## Memory Usage Statistics (%)\n\n")
        f.write("| Process Name | Mean | Min | Max | Median | P90 | P99 | Std |\n")
        f.write("|--------------|------|-----|-----|--------|-----|-----|-----|\n")
        
        for process, stat in mem_stats.items():
            f.write(f"| {process} | {stat['mean']:.2f} | {stat['min']:.2f} | {stat['max']:.2f} | "
                   f"{stat['median']:.2f} | {stat['p90']:.2f} | {stat['p99']:.2f} | {stat['std']:.2f} |\n")
        
        # Write summary
        f.write("\n## Summary\n\n")
        f.write("### Top CPU Usage Processes\n\n")
        cpu_sorted = sorted(cpu_stats.items(), key=lambda x: x[1]['mean'], reverse=True)[:5]
        for i, (process, stat) in enumerate(cpu_sorted, 1):
            f.write(f"{i}. **{process}**: {stat['mean']:.2f}% (avg), {stat['max']:.2f}% (max)\n")
        
        f.write("\n### Top Memory Usage Processes\n\n")
        mem_sorted = sorted(mem_stats.items(), key=lambda x: x[1]['mean'], reverse=True)[:5]
        for i, (process, stat) in enumerate(mem_sorted, 1):
            f.write(f"{i}. **{process}**: {stat['mean']:.2f}% (avg), {stat['max']:.2f}% (max)\n")
        
        f.write("\n---\n")
        f.write("*Report generated automatically from monitoring data*\n")

if __name__ == "__main__":
    print("Loading monitoring data...")
    df = parse_monitor_data('monitor_status_sz_2025-06-25.csv')
    
    print(f"Data loaded successfully!")
    print(f"Total records: {len(df)}")
    print(f"Number of processes: {df['process_name'].nunique()}")
    print(f"Time range: {(df['timestamp'].max() - df['timestamp'].min()):.1f} seconds")
    
    # Calculate statistics
    cpu_stats = calculate_statistics(df, 'cpu_usage')
    mem_stats = calculate_statistics(df, 'mem_usage')
    
    # Generate markdown report
    write_statistics_to_markdown(cpu_stats, mem_stats, df, 'analysis_report.md')
    print("Statistics report saved as 'analysis_report.md'")
    print("Analysis completed!")
