#!/usr/bin/env python3

import pandas as pd
import numpy as np

def parse_monitor_data(filename):
    data = []
    current_timestamp = None
    
    with open(filename, 'r') as file:
        for line in file:
            line = line.strip()
            if not line:
                continue
                
            if line.startswith('timestamp,'):
                current_timestamp = float(line.split(',')[1].strip())
            elif line.startswith('name,') and current_timestamp is not None:
                parts = [part.strip() for part in line.split(',')]
                if len(parts) >= 8:
                    process_name = parts[1]
                    pid = int(parts[3])
                    cpu_usage = float(parts[5])
                    mem_usage = float(parts[7])
                    
                    data.append({
                        'timestamp': current_timestamp,
                        'process_name': process_name,
                        'pid': pid,
                        'cpu_usage': cpu_usage,
                        'mem_usage': mem_usage
                    })
    
    return pd.DataFrame(data)

def calculate_statistics(df, metric):
    stats = {}
    for process in df['process_name'].unique():
        process_data = df[df['process_name'] == process][metric]
        stats[process] = {
            'mean': process_data.mean(),
            'min': process_data.min(),
            'max': process_data.max(),
            'median': process_data.median(),
            'p90': process_data.quantile(0.9),
            'p99': process_data.quantile(0.99),
            'std': process_data.std()
        }
    return stats

def print_statistics_table(stats, metric_name):
    print("\n" + "="*80)
    print(f"{metric_name} Statistics Analysis")
    print("="*80)
    header = f"{'Process Name':<20} {'Mean':<8} {'Min':<8} {'Max':<8} {'Median':<8} {'P90':<8} {'P99':<8} {'Std':<8}"
    print(header)
    print("-" * 80)
    
    for process, stat in stats.items():
        row = f"{process:<20} {stat['mean']:<8.2f} {stat['min']:<8.2f} {stat['max']:<8.2f} {stat['median']:<8.2f} {stat['p90']:<8.2f} {stat['p99']:<8.2f} {stat['std']:<8.2f}"
        print(row)

if __name__ == "__main__":
    print("Loading monitoring data...")
    df = parse_monitor_data('monitor_status_sz_2025-06-25.csv')
    
    print(f"Data loaded successfully!")
    print(f"Total records: {len(df)}")
    print(f"Number of processes: {df['process_name'].nunique()}")
    print(f"Time range: {(df['timestamp'].max() - df['timestamp'].min()):.1f} seconds")
    print(f"Process list: {', '.join(df['process_name'].unique())}")
    
    cpu_stats = calculate_statistics(df, 'cpu_usage')
    mem_stats = calculate_statistics(df, 'mem_usage')
    
    print_statistics_table(cpu_stats, "CPU Usage (%)")
    print_statistics_table(mem_stats, "Memory Usage (%)")
    
    print("\nAnalysis completed!")
