# Process Monitoring Analysis Report

## Basic Information

- **Total records**: 1045
- **Number of processes**: 11
- **Time range**: 114.0 seconds
- **Process list**: left_right_ac, head_ac, head_camera, rviz2, mocap, realsense2_camera, ros2_monitor, motion_capture_node, right_arm_host_node, robot_control_node, robot_hci_node

## CPU Usage Statistics (%)

| Process Name | Mean | Min | Max | Median | P90 | P99 | Std |
|--------------|------|-----|-----|--------|-----|-----|-----|
| left_right_ac | 37.12 | 35.14 | 38.90 | 37.05 | 38.31 | 38.70 | 0.89 |
| head_ac | 87.12 | 83.84 | 88.67 | 87.26 | 88.41 | 88.66 | 1.23 |
| head_camera | 50.89 | 38.08 | 54.87 | 51.17 | 53.43 | 54.42 | 3.45 |
| rviz2 | 68.45 | 61.55 | 76.12 | 68.27 | 73.40 | 75.75 | 3.21 |
| mocap | 3.42 | 2.81 | 3.75 | 3.27 | 3.75 | 3.75 | 0.28 |
| realsense2_camera | 107.89 | 87.99 | 114.07 | 108.73 | 112.32 | 113.18 | 5.67 |
| ros2_monitor | 6.32 | 5.59 | 7.02 | 6.09 | 6.56 | 6.99 | 0.31 |
| motion_capture_node | 89.45 | 36.57 | 106.43 | 101.11 | 105.40 | 106.33 | 18.92 |
| right_arm_host_node | 6.12 | 3.73 | 8.90 | 7.95 | 8.42 | 8.87 | 1.89 |
| robot_control_node | 9.78 | 7.04 | 12.65 | 10.77 | 11.71 | 12.16 | 1.45 |
| robot_hci_node | 1.12 | 0.46 | 1.87 | 0.94 | 1.40 | 1.87 | 0.42 |

## Memory Usage Statistics (%)

| Process Name | Mean | Min | Max | Median | P90 | P99 | Std |
|--------------|------|-----|-----|--------|-----|-----|-----|
| left_right_ac | 0.446 | 0.442 | 0.449 | 0.449 | 0.449 | 0.449 | 0.003 |
| head_ac | 0.210 | 0.208 | 0.280 | 0.209 | 0.209 | 0.280 | 0.007 |
| head_camera | 0.295 | 0.278 | 0.302 | 0.300 | 0.302 | 0.302 | 0.011 |
| rviz2 | 0.387 | 0.376 | 0.395 | 0.385 | 0.395 | 0.395 | 0.005 |
| mocap | 0.039 | 0.039 | 0.039 | 0.039 | 0.039 | 0.039 | 0.000 |
| realsense2_camera | 0.216 | 0.191 | 0.219 | 0.216 | 0.218 | 0.219 | 0.006 |
| ros2_monitor | 0.037 | 0.037 | 0.037 | 0.037 | 0.037 | 0.037 | 0.000 |
| motion_capture_node | 1.069 | 1.022 | 1.081 | 1.075 | 1.081 | 1.081 | 0.018 |
| right_arm_host_node | 0.046 | 0.046 | 0.047 | 0.046 | 0.047 | 0.047 | 0.000 |
| robot_control_node | 0.048 | 0.047 | 0.048 | 0.048 | 0.048 | 0.048 | 0.000 |
| robot_hci_node | 0.060 | 0.059 | 0.062 | 0.060 | 0.060 | 0.062 | 0.001 |

## Summary

### Top CPU Usage Processes

1. **realsense2_camera**: 107.89% (avg), 114.07% (max)
2. **motion_capture_node**: 89.45% (avg), 106.43% (max)
3. **head_ac**: 87.12% (avg), 88.67% (max)
4. **rviz2**: 68.45% (avg), 76.12% (max)
5. **head_camera**: 50.89% (avg), 54.87% (max)

### Top Memory Usage Processes

1. **motion_capture_node**: 1.07% (avg), 1.08% (max)
2. **left_right_ac**: 0.45% (avg), 0.45% (max)
3. **rviz2**: 0.39% (avg), 0.40% (max)
4. **head_camera**: 0.30% (avg), 0.30% (max)
5. **realsense2_camera**: 0.22% (avg), 0.22% (max)

## Key Observations

### CPU Usage Analysis
- **realsense2_camera** is the most CPU-intensive process, averaging 107.89% CPU usage
- **motion_capture_node** shows high variability (std: 18.92) with usage ranging from 36.57% to 106.43%
- **head_ac** maintains consistently high CPU usage around 87%
- Several processes exceed 100% CPU usage, indicating multi-core utilization

### Memory Usage Analysis
- **motion_capture_node** uses the most memory at ~1.07%
- Overall memory usage is relatively low across all processes
- Memory usage is quite stable with low standard deviations

### Performance Recommendations
1. Monitor **realsense2_camera** for potential optimization opportunities
2. Investigate **motion_capture_node** CPU usage spikes
3. Consider load balancing for high CPU usage processes
4. Memory usage appears well-optimized across all processes

---
*Report generated automatically from monitoring data*
