#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

def parse_monitor_data(filename):
    """
    Parse monitoring data CSV file
    """
    data = []
    current_timestamp = None
    
    with open(filename, 'r') as file:
        for line in file:
            line = line.strip()
            if not line:
                continue
                
            # Parse timestamp line
            if line.startswith('timestamp,'):
                current_timestamp = float(line.split(',')[1].strip())
            
            # Parse process data line
            elif line.startswith('name,') and current_timestamp is not None:
                parts = [part.strip() for part in line.split(',')]
                if len(parts) >= 8:
                    process_name = parts[1]
                    pid = int(parts[3])
                    cpu_usage = float(parts[5])
                    mem_usage = float(parts[7])
                    
                    data.append({
                        'timestamp': current_timestamp,
                        'process_name': process_name,
                        'pid': pid,
                        'cpu_usage': cpu_usage,
                        'mem_usage': mem_usage
                    })
    
    return pd.DataFrame(data)

def calculate_statistics(df, metric):
    """
    Calculate statistics: mean, min/max, median, p99, p90
    """
    stats = {}
    for process in df['process_name'].unique():
        process_data = df[df['process_name'] == process][metric]
        stats[process] = {
            'mean': process_data.mean(),
            'min': process_data.min(),
            'max': process_data.max(),
            'median': process_data.median(),
            'p90': process_data.quantile(0.9),
            'p99': process_data.quantile(0.99),
            'std': process_data.std()
        }
    return stats

def plot_metrics(df, metric, title, ylabel):
    """
    Plot metric curves
    """
    plt.figure(figsize=(15, 10))
    
    # Get all process names
    processes = df['process_name'].unique()
    colors = plt.cm.tab10(np.linspace(0, 1, len(processes)))
    
    for i, process in enumerate(processes):
        process_data = df[df['process_name'] == process]
        # Convert timestamp to relative time (seconds)
        relative_time = (process_data['timestamp'] - df['timestamp'].min())
        plt.plot(relative_time, process_data[metric], 
                label=process, color=colors[i], linewidth=1.5, alpha=0.8)
    
    plt.title(title, fontsize=16, fontweight='bold')
    plt.xlabel('Time (seconds)', fontsize=12)
    plt.ylabel(ylabel, fontsize=12)
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    return plt

def print_statistics_table(stats, metric_name):
    """
    Print statistics table
    """
    print("\n" + "="*80)
    print(f"{metric_name} Statistics Analysis")
    print("="*80)
    print(f"{'Process Name':<20} {'Mean':<8} {'Min':<8} {'Max':<8} {'Median':<8} {'P90':<8} {'P99':<8} {'Std':<8}")
    print("-" * 80)
    
    for process, stat in stats.items():
        print(f"{process:<20} {stat['mean']:<8.2f} {stat['min']:<8.2f} {stat['max']:<8.2f} "
              f"{stat['median']:<8.2f} {stat['p90']:<8.2f} {stat['p99']:<8.2f} {stat['std']:<8.2f}")

# Main program
if __name__ == "__main__":
    # Load and parse data
    print("Loading monitoring data...")
    df = parse_monitor_data('monitor_status_sz_2025-06-25.csv')
    
    print(f"Data loaded successfully!")
    print(f"Total records: {len(df)}")
    print(f"Number of processes: {df['process_name'].nunique()}")
    print(f"Time range: {(df['timestamp'].max() - df['timestamp'].min()):.1f} seconds")
    print(f"Process list: {', '.join(df['process_name'].unique())}")
    
    # Calculate statistics
    cpu_stats = calculate_statistics(df, 'cpu_usage')
    mem_stats = calculate_statistics(df, 'mem_usage')
    
    # Print statistics tables
    print_statistics_table(cpu_stats, "CPU Usage (%)")
    print_statistics_table(mem_stats, "Memory Usage (%)")
    
    # Plot CPU usage curves
    plt.style.use('default')
    plot_metrics(df, 'cpu_usage', 'Process CPU Usage Monitoring', 'CPU Usage (%)')
    plt.savefig('cpu_usage_plot.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Plot memory usage curves
    plot_metrics(df, 'mem_usage', 'Process Memory Usage Monitoring', 'Memory Usage (%)')
    plt.savefig('memory_usage_plot.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("\nCharts saved as 'cpu_usage_plot.png' and 'memory_usage_plot.png'")
    print("Analysis completed!")
