<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="DuplicatedCode" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <Languages>
        <language minSize="51" name="Python" />
      </Languages>
    </inspection_tool>
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="107">
            <item index="0" class="java.lang.String" itemvalue="roslz4" />
            <item index="1" class="java.lang.String" itemvalue="sensor-msgs" />
            <item index="2" class="java.lang.String" itemvalue="rqt_dep" />
            <item index="3" class="java.lang.String" itemvalue="rosgraph" />
            <item index="4" class="java.lang.String" itemvalue="tf-conversions" />
            <item index="5" class="java.lang.String" itemvalue="rostest" />
            <item index="6" class="java.lang.String" itemvalue="rqt_service_caller" />
            <item index="7" class="java.lang.String" itemvalue="rosunit" />
            <item index="8" class="java.lang.String" itemvalue="rqt_srv" />
            <item index="9" class="java.lang.String" itemvalue="rospy" />
            <item index="10" class="java.lang.String" itemvalue="rqt_launch" />
            <item index="11" class="java.lang.String" itemvalue="diagnostic-analysis" />
            <item index="12" class="java.lang.String" itemvalue="joint-state-publisher" />
            <item index="13" class="java.lang.String" itemvalue="rosmsg" />
            <item index="14" class="java.lang.String" itemvalue="rqt_action" />
            <item index="15" class="java.lang.String" itemvalue="rqt_gui_py" />
            <item index="16" class="java.lang.String" itemvalue="roslib" />
            <item index="17" class="java.lang.String" itemvalue="gazebo_plugins" />
            <item index="18" class="java.lang.String" itemvalue="rqt_bag" />
            <item index="19" class="java.lang.String" itemvalue="laser_geometry" />
            <item index="20" class="java.lang.String" itemvalue="rqt_bag_plugins" />
            <item index="21" class="java.lang.String" itemvalue="rqt_py_console" />
            <item index="22" class="java.lang.String" itemvalue="rqt-robot-dashboard" />
            <item index="23" class="java.lang.String" itemvalue="rviz" />
            <item index="24" class="java.lang.String" itemvalue="rqt-robot-monitor" />
            <item index="25" class="java.lang.String" itemvalue="rqt_logger_level" />
            <item index="26" class="java.lang.String" itemvalue="rqt_runtime_monitor" />
            <item index="27" class="java.lang.String" itemvalue="catkin" />
            <item index="28" class="java.lang.String" itemvalue="roswtf" />
            <item index="29" class="java.lang.String" itemvalue="rqt-reconfigure" />
            <item index="30" class="java.lang.String" itemvalue="cv-bridge" />
            <item index="31" class="java.lang.String" itemvalue="qt-gui-cpp" />
            <item index="32" class="java.lang.String" itemvalue="tf2-py" />
            <item index="33" class="java.lang.String" itemvalue="diagnostic-updater" />
            <item index="34" class="java.lang.String" itemvalue="rqt_shell" />
            <item index="35" class="java.lang.String" itemvalue="gennodejs" />
            <item index="36" class="java.lang.String" itemvalue="message-filters" />
            <item index="37" class="java.lang.String" itemvalue="actionlib" />
            <item index="38" class="java.lang.String" itemvalue="rqt_topic" />
            <item index="39" class="java.lang.String" itemvalue="tf2-geometry-msgs" />
            <item index="40" class="java.lang.String" itemvalue="diagnostic-common-diagnostics" />
            <item index="41" class="java.lang.String" itemvalue="roslaunch" />
            <item index="42" class="java.lang.String" itemvalue="dynamic-reconfigure" />
            <item index="43" class="java.lang.String" itemvalue="resource_retriever" />
            <item index="44" class="java.lang.String" itemvalue="rqt_robot_steering" />
            <item index="45" class="java.lang.String" itemvalue="smach" />
            <item index="46" class="java.lang.String" itemvalue="tf" />
            <item index="47" class="java.lang.String" itemvalue="qt-gui-py-common" />
            <item index="48" class="java.lang.String" itemvalue="controller-manager" />
            <item index="49" class="java.lang.String" itemvalue="genpy" />
            <item index="50" class="java.lang.String" itemvalue="rosboost-cfg" />
            <item index="51" class="java.lang.String" itemvalue="rqt_py_common" />
            <item index="52" class="java.lang.String" itemvalue="bondpy" />
            <item index="53" class="java.lang.String" itemvalue="rqt_pose_view" />
            <item index="54" class="java.lang.String" itemvalue="rqt_tf_tree" />
            <item index="55" class="java.lang.String" itemvalue="gazebo_ros" />
            <item index="56" class="java.lang.String" itemvalue="rqt_graph" />
            <item index="57" class="java.lang.String" itemvalue="rqt_image_view" />
            <item index="58" class="java.lang.String" itemvalue="camera-calibration" />
            <item index="59" class="java.lang.String" itemvalue="rqt_plot" />
            <item index="60" class="java.lang.String" itemvalue="joint-state-publisher-gui" />
            <item index="61" class="java.lang.String" itemvalue="topic-tools" />
            <item index="62" class="java.lang.String" itemvalue="controller-manager-msgs" />
            <item index="63" class="java.lang.String" itemvalue="xacro" />
            <item index="64" class="java.lang.String" itemvalue="rosnode" />
            <item index="65" class="java.lang.String" itemvalue="rqt-rviz" />
            <item index="66" class="java.lang.String" itemvalue="rqt_console" />
            <item index="67" class="java.lang.String" itemvalue="rosbag" />
            <item index="68" class="java.lang.String" itemvalue="rosmaster" />
            <item index="69" class="java.lang.String" itemvalue="rqt_nav_view" />
            <item index="70" class="java.lang.String" itemvalue="roslint" />
            <item index="71" class="java.lang.String" itemvalue="rosmake" />
            <item index="72" class="java.lang.String" itemvalue="genmsg" />
            <item index="73" class="java.lang.String" itemvalue="rqt_publisher" />
            <item index="74" class="java.lang.String" itemvalue="image-geometry" />
            <item index="75" class="java.lang.String" itemvalue="roscreate" />
            <item index="76" class="java.lang.String" itemvalue="rqt_msg" />
            <item index="77" class="java.lang.String" itemvalue="tf2-ros" />
            <item index="78" class="java.lang.String" itemvalue="rqt_top" />
            <item index="79" class="java.lang.String" itemvalue="rqt_web" />
            <item index="80" class="java.lang.String" itemvalue="angles" />
            <item index="81" class="java.lang.String" itemvalue="rqt-moveit" />
            <item index="82" class="java.lang.String" itemvalue="interactive-markers" />
            <item index="83" class="java.lang.String" itemvalue="rosclean" />
            <item index="84" class="java.lang.String" itemvalue="tf2-kdl" />
            <item index="85" class="java.lang.String" itemvalue="qt-dotgraph" />
            <item index="86" class="java.lang.String" itemvalue="rosservice" />
            <item index="87" class="java.lang.String" itemvalue="gencpp" />
            <item index="88" class="java.lang.String" itemvalue="genlisp" />
            <item index="89" class="java.lang.String" itemvalue="rosparam" />
            <item index="90" class="java.lang.String" itemvalue="camera-calibration-parsers" />
            <item index="91" class="java.lang.String" itemvalue="python-qt-binding" />
            <item index="92" class="java.lang.String" itemvalue="qt-gui" />
            <item index="93" class="java.lang.String" itemvalue="smach-ros" />
            <item index="94" class="java.lang.String" itemvalue="rostopic" />
            <item index="95" class="java.lang.String" itemvalue="geneus" />
            <item index="96" class="java.lang.String" itemvalue="smclib" />
            <item index="97" class="java.lang.String" itemvalue="rqt_gui" />
            <item index="98" class="java.lang.String" itemvalue="gradio-rangeslider" />
            <item index="99" class="java.lang.String" itemvalue="fastapi" />
            <item index="100" class="java.lang.String" itemvalue="uvicorn" />
            <item index="101" class="java.lang.String" itemvalue="python-multipart" />
            <item index="102" class="java.lang.String" itemvalue="onnxruntime" />
            <item index="103" class="java.lang.String" itemvalue="pydantic" />
            <item index="104" class="java.lang.String" itemvalue="asyncpg" />
            <item index="105" class="java.lang.String" itemvalue="jose" />
            <item index="106" class="java.lang.String" itemvalue="pydantic_settings" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8Inspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="E501" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N802" />
          <option value="N803" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyShadowingNamesInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
  </profile>
</component>