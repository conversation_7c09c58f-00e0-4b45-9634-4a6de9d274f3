
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

# Set matplotlib to support Chinese fonts, use English if Chinese fonts are not available
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False  # Fix minus sign display issue
except:
    # If setting Chinese fonts fails, use default fonts
    pass


def parse_monitor_data(filename):
    """
    解析监控数据CSV文件
    """
    data = []
    current_timestamp = None

    with open(filename, 'r') as file:
        for line in file:
            line = line.strip()
            if not line:
                continue

            # 解析时间戳行
            if line.startswith('timestamp,'):
                current_timestamp = float(line.split(',')[1].strip())

            # 解析进程数据行
            elif line.startswith('name,') and current_timestamp is not None:
                parts = [part.strip() for part in line.split(',')]
                if len(parts) >= 8:
                    process_name = parts[1]
                    pid = int(parts[3])
                    cpu_usage = float(parts[5])
                    mem_usage = float(parts[7])

                    data.append({
                        'timestamp': current_timestamp,
                        'process_name': process_name,
                        'pid': pid,
                        'cpu_usage': cpu_usage,
                        'mem_usage': mem_usage
                    })

    return pd.DataFrame(data)


def calculate_statistics(df, metric):
    """
    计算统计指标：均值、极值、中值、p99、p90
    """
    stats = {}
    for process in df['process_name'].unique():
        process_data = df[df['process_name'] == process][metric]
        stats[process] = {
            'mean': process_data.mean(),
            'min': process_data.min(),
            'max': process_data.max(),
            'median': process_data.median(),
            'p90': process_data.quantile(0.9),
            'p99': process_data.quantile(0.99),
            'std': process_data.std()
        }
    return stats


def plot_metrics(df, metric, title, ylabel):
    """
    绘制指标曲线图
    """
    plt.figure(figsize=(15, 10))

    # 获取所有进程名
    processes = df['process_name'].unique()
    colors = plt.cm.tab10(np.linspace(0, 1, len(processes)))

    for i, process in enumerate(processes):
        process_data = df[df['process_name'] == process]
        # 将时间戳转换为相对时间（秒）
        relative_time = (process_data['timestamp'] - df['timestamp'].min())
        plt.plot(relative_time, process_data[metric],
                label=process, color=colors[i], linewidth=1.5, alpha=0.8)

    plt.title(title, fontsize=16, fontweight='bold')
    plt.xlabel('Time (seconds)', fontsize=12)
    plt.ylabel(ylabel, fontsize=12)
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    return plt


def print_statistics_table(stats, metric_name):
    """
    打印统计表格
    """
    print(f"\n{'='*80}")
    print(f"{metric_name} 统计分析")
    print("="*80)
    print(f"{'进程名':<20} {'均值':<8} {'最小值':<8} {'最大值':<8} {'中值':<8} {'P90':<8} {'P99':<8} {'标准差':<8}")
    print("-" * 80)

    for process, stat in stats.items():
        print(f"{process:<20} {stat['mean']:<8.2f} {stat['min']:<8.2f} {stat['max']:<8.2f} "
              f"{stat['median']:<8.2f} {stat['p90']:<8.2f} {stat['p99']:<8.2f} {stat['std']:<8.2f}")


def write_statistics_to_markdown(cpu_stats, mem_stats, df, filename='analysis_report.md'):
    """
    Write statistics to markdown file
    """
    with open(filename, 'w', encoding='utf-8') as f:
        # Write header
        f.write("# Process Monitoring Analysis Report\n\n")

        # Write basic info
        f.write("## Basic Information\n\n")
        f.write(f"- **Total records**: {len(df)}\n")
        f.write(f"- **Number of processes**: {df['process_name'].nunique()}\n")
        f.write(f"- **Time range**: {(df['timestamp'].max() - df['timestamp'].min()):.1f} seconds\n")
        f.write(f"- **Process list**: {', '.join(df['process_name'].unique())}\n\n")

        # Write CPU statistics
        f.write("## CPU Usage Statistics (%)\n\n")
        f.write("| Process Name | Mean | Min | Max | Median | P90 | P99 | Std |\n")
        f.write("|--------------|------|-----|-----|--------|-----|-----|-----|\n")

        for process, stat in cpu_stats.items():
            f.write(f"| {process} | {stat['mean']:.2f} | {stat['min']:.2f} | {stat['max']:.2f} | "
                   f"{stat['median']:.2f} | {stat['p90']:.2f} | {stat['p99']:.2f} | {stat['std']:.2f} |\n")

        # Write Memory statistics
        f.write("\n## Memory Usage Statistics (%)\n\n")
        f.write("| Process Name | Mean | Min | Max | Median | P90 | P99 | Std |\n")
        f.write("|--------------|------|-----|-----|--------|-----|-----|-----|\n")

        for process, stat in mem_stats.items():
            f.write(f"| {process} | {stat['mean']:.2f} | {stat['min']:.2f} | {stat['max']:.2f} | "
                   f"{stat['median']:.2f} | {stat['p90']:.2f} | {stat['p99']:.2f} | {stat['std']:.2f} |\n")

        # Write summary
        f.write("\n## Summary\n\n")
        f.write("### Top CPU Usage Processes\n")
        cpu_sorted = sorted(cpu_stats.items(), key=lambda x: x[1]['mean'], reverse=True)[:5]
        for i, (process, stat) in enumerate(cpu_sorted, 1):
            f.write(f"{i}. **{process}**: {stat['mean']:.2f}% (avg), {stat['max']:.2f}% (max)\n")

        f.write("\n### Top Memory Usage Processes\n")
        mem_sorted = sorted(mem_stats.items(), key=lambda x: x[1]['mean'], reverse=True)[:5]
        for i, (process, stat) in enumerate(mem_sorted, 1):
            f.write(f"{i}. **{process}**: {stat['mean']:.2f}% (avg), {stat['max']:.2f}% (max)\n")

        f.write("\n---\n")
        f.write("*Report generated automatically from monitoring data*\n")


if __name__ == "__main__":
    # 读取和解析数据
    print("正在读取监控数据...")
    df = parse_monitor_data('monitor_status_sz_2025-06-25.csv')

    print(f"数据加载完成！")
    print(f"总记录数: {len(df)}")
    print(f"进程数量: {df['process_name'].nunique()}")
    print(f"时间范围: {(df['timestamp'].max() - df['timestamp'].min()):.1f} 秒")
    print(f"进程列表: {', '.join(df['process_name'].unique())}")

    # 计算统计数据
    cpu_stats = calculate_statistics(df, 'cpu_usage')
    mem_stats = calculate_statistics(df, 'mem_usage')

    # 打印统计表格
    print_statistics_table(cpu_stats, "CPU使用率 (%)")
    print_statistics_table(mem_stats, "内存使用率 (%)")

    # 绘制CPU使用率曲线图
    plt.style.use('default')
    plot_metrics(df, 'cpu_usage', 'Process CPU Usage Monitoring', 'CPU Usage (%)')
    plt.savefig('cpu_usage_plot.png', dpi=300, bbox_inches='tight')

    # 绘制内存使用率曲线图
    plot_metrics(df, 'mem_usage', 'Process Memory Usage Monitoring', 'Memory Usage (%)')
    plt.savefig('memory_usage_plot.png', dpi=300, bbox_inches='tight')

    print("\n图表已保存为 'cpu_usage_plot.png' 和 'memory_usage_plot.png'")
    print("分析完成！")
